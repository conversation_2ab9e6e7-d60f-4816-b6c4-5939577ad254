package com.yooa.crm.api.domain.query;

import com.yooa.common.mybatis.base.QueryEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 投放订单
 */
@Data
public class RechargeRecordPitcherQuery extends QueryEntity {
    /**
     * 搜索框
     */
    private String searchBox;
    /**
     * 平台
     */
    private Long appProject;


    /**
     * 推广部门id
     */
    private Long extendDeptId;

    /**
     * 投手部门id
     */
    private Long pitcherDeptId;

    /**
     * 推广部门id列表（包含下级部门）
     */
    private List<Long> extendDeptIds;

    /**
     * 投手部门id列表（包含下级部门）
     */
    private List<Long> pitcherDeptIds;

    /**
     * 订单开始时间
     */
    private LocalDateTime startOrderTime;

    /**
     * 订单结束时间
     */
    private LocalDateTime endOrderTime;

    /**
     * 订单状态(1:已完成、2:已退款、3:特殊退款)
     */
    private Integer orderStatus;

    /**
     * 支付方式
     */
    private Integer paymentType;

    /**
     * 客户开始时间
     */
    private LocalDateTime startCustomerTime;
    /**
     * 客户结束时间
     */
    private LocalDateTime endCustomerTime;

    /**
     * 查询类型（1：首次下单，2：45天内下单）
     */
    private Long type;


}
